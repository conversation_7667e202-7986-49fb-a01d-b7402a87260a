{"transactions": [{"hash": "0xf53b6c84a8fc96f012ed9f9208c254c5ff1ddac6ddf6b45ad0ef8cfbec8d18e9", "transactionType": "CREATE", "contractName": "MockERC20", "contractAddress": "0x9594b561eeca510c04f5d12d0ac2c594a395e186", "function": null, "arguments": null, "transaction": {"from": "0xf39fd6e51aad88f6f4ce6ab8827279cfffb92266", "gas": "0x10788e", "value": "0x0", "input": "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", "nonce": "0x330b", "chainId": "0x53a"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": "0x923b7be9ec09c9ebfa8b4b94dc083ba0546f2a8b0507074813f3298faa7cacc7", "transactionType": "CALL", "contractName": "MockERC20", "contractAddress": "0x9594b561eeca510c04f5d12d0ac2c594a395e186", "function": "initialize(string,string,uint8)", "arguments": ["Mock USDC", "mUSDC", "6"], "transaction": {"from": "0xf39fd6e51aad88f6f4ce6ab8827279cfffb92266", "to": "0x9594b561eeca510c04f5d12d0ac2c594a395e186", "gas": "0x382a0", "value": "0x0", "input": "0x1624f6c6000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000a0000000000000000000000000000000000000000000000000000000000000000600000000000000000000000000000000000000000000000000000000000000094d6f636b2055534443000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000056d55534443000000000000000000000000000000000000000000000000000000", "nonce": "0x330c", "chainId": "0x53a"}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [{"status": "0x1", "cumulativeGasUsed": "0xd6c28", "logs": [], "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "type": "0x2", "transactionHash": "0xf53b6c84a8fc96f012ed9f9208c254c5ff1ddac6ddf6b45ad0ef8cfbec8d18e9", "transactionIndex": "0x1", "blockHash": "0x9fa30170969b256bae5e235b6bd416823afc46deb6c4dc285e272b7fe5a28338", "blockNumber": "0x8fb42", "gasUsed": "0xcab81", "effectiveGasPrice": "0x8", "from": "0xf39fd6e51aad88f6f4ce6ab8827279cfffb92266", "to": null, "contractAddress": "0x9594b561eeca510c04f5d12d0ac2c594a395e186", "l1Fee": "0x0", "l1FeeScalar": "0", "l1GasPrice": "0x7", "l1GasUsed": "0x88fb"}, {"status": "0x1", "cumulativeGasUsed": "0xfd29a", "logs": [], "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "type": "0x2", "transactionHash": "0x923b7be9ec09c9ebfa8b4b94dc083ba0546f2a8b0507074813f3298faa7cacc7", "transactionIndex": "0x2", "blockHash": "0x9fa30170969b256bae5e235b6bd416823afc46deb6c4dc285e272b7fe5a28338", "blockNumber": "0x8fb42", "gasUsed": "0x26672", "effectiveGasPrice": "0x8", "from": "0xf39fd6e51aad88f6f4ce6ab8827279cfffb92266", "to": "0x9594b561eeca510c04f5d12d0ac2c594a395e186", "contractAddress": null, "l1Fee": "0x0", "l1FeeScalar": "0", "l1GasPrice": "0x7", "l1GasUsed": "0x640"}], "libraries": [], "pending": [], "returns": {}, "timestamp": 1738801244, "chain": 1338, "commit": "aaeac47"}