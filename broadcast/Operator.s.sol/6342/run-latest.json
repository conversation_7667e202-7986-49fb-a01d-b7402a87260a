{"transactions": [{"hash": "0x61fbec66fc62a01b4c6e9f5b9124054557e84bfec0a454cc5f83c5302b7ad6cc", "transactionType": "CREATE", "contractName": "Operator", "contractAddress": "0x9c68a08a010ecb812ff4549be88b747ff73b5bc6", "function": null, "arguments": null, "transaction": {"from": "0xd999ec8afacc3ddd99226adde834f1d29647d3ba", "gas": "0x4af74", "value": "0x0", "input": "0x6080604052348015600e575f5ffd5b5061034e8061001c5f395ff3fe608060405234801561000f575f5ffd5b506004361061003f575f3560e01c80631c3ecdc6146100435780637e563ba314610058578063e15e68a81461007d575b5f5ffd5b610056610051366004610280565b610090565b005b61006b6100663660046102a8565b610109565b60405190815260200160405180910390f35b61005661008b366004610280565b610140565b5f6100996101ae565b335f818152602083815260408083206001600160a01b0389168085529252909120805486198116909155929350906100cf61020f565b6040518681527f1145ef8300109b8668d5581d376603c552d28f5aaefa3ca8fb7524286a41a7ae906020015b60405180910390a450505050565b5f6101126101ae565b6001600160a01b038085165f9081526020928352604080822092861682529190925290205490505b92915050565b5f6101496101ae565b335f818152602083815260408083206001600160a01b038916808552925290912080548681179091559293509061017e61020f565b6040518681527fb816c81e0d2e75687754a9cb3111541c16ab454792482bf1dd02093f2203f353906020016100fb565b5f8060ff196101de60017f3f6ff8f7a23e63df1ae11cf0e622621ffe0d906ff23598a5b233af0d05cc54966102ed565b6040516020016101f091815260200190565b60408051601f1981840301815291905280516020909101201692915050565b5f5f610219610235565b9050805f015f815461022a90610300565b918290555092915050565b5f8060ff196101de60017fbf758bc9c92b14fd6317671867aa918ecfcd5d38762f91055fd53b96370d2c116102ed565b80356001600160a01b038116811461027b575f5ffd5b919050565b5f5f60408385031215610291575f5ffd5b61029a83610265565b946020939093013593505050565b5f5f604083850312156102b9575f5ffd5b6102c283610265565b91506102d060208401610265565b90509250929050565b634e487b7160e01b5f52601160045260245ffd5b8181038181111561013a5761013a6102d9565b5f60018201610311576103116102d9565b506001019056fea264697066735822122047384cd65060b7c317b30d12fa9c98f1d1f8469aa07227d5b3f320d873e1650364736f6c634300081b0033", "nonce": "0x17c9d6", "chainId": "0x18c6"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": "0xf4cba8f9e1bab98300754a8e40cda58cc5481c5b5e4e52b75490c719ebd86a6e", "transactionType": "CALL", "contractName": null, "contractAddress": "0xf7568f77fdb10c084a20c81e1d58576ab4ec850f", "function": "deploy(address,address)", "arguments": ["0x9C68a08A010ECb812ff4549be88b747FF73B5bc6", "0xD999ec8aFAcC3ddD99226adde834f1d29647d3ba"], "transaction": {"from": "0xd999ec8afacc3ddd99226adde834f1d29647d3ba", "to": "0xf7568f77fdb10c084a20c81e1d58576ab4ec850f", "gas": "0x2a912", "value": "0x0", "input": "0x545e7c610000000000000000000000009c68a08a010ecb812ff4549be88b747ff73b5bc6000000000000000000000000d999ec8afacc3ddd99226adde834f1d29647d3ba", "nonce": "0x17c9d7", "chainId": "0x18c6"}, "additionalContracts": [{"transactionType": "CREATE", "address": "0x9dc88e61178f8f6f688247b7275002241a9f7540", "initCode": "0x607f3d8160093d39f33d3d3373f7568f77fdb10c084a20c81e1d58576ab4ec850f14605757363d3d37363d7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc545af43d6000803e6052573d6000fd5b3d6000f35b3d356020355560408036111560525736038060403d373d3d355af43d6000803e6052573d6000fd"}], "isFixedGasLimit": false}], "receipts": [{"status": "0x1", "cumulativeGasUsed": "0x5e0abb", "logs": [], "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "type": "0x2", "transactionHash": "0x61fbec66fc62a01b4c6e9f5b9124054557e84bfec0a454cc5f83c5302b7ad6cc", "transactionIndex": "0x29", "blockHash": "0x0000000000000000000000000000000000000000000000000000000000000000", "blockNumber": "0xb27029", "gasUsed": "0x39aa8", "effectiveGasPrice": "0xf4241", "from": "0xd999ec8afacc3ddd99226adde834f1d29647d3ba", "to": null, "contractAddress": "0x9c68a08a010ecb812ff4549be88b747ff73b5bc6"}, {"status": "0x1", "cumulativeGasUsed": "0x11f330a", "logs": [{"address": "0xf7568f77fdb10c084a20c81e1d58576ab4ec850f", "topics": ["0xc95935a66d15e0da5e412aca0ad27ae891d20b2fb91cf3994b6a3bf2b8178082", "0x0000000000000000000000009dc88e61178f8f6f688247b7275002241a9f7540", "0x0000000000000000000000009c68a08a010ecb812ff4549be88b747ff73b5bc6", "0x000000000000000000000000d999ec8afacc3ddd99226adde834f1d29647d3ba"], "data": "0x", "blockHash": "0x0000000000000000000000000000000000000000000000000000000000000000", "blockNumber": "0xb27029", "blockTimestamp": "0x687ac0bb", "transactionHash": "0xf4cba8f9e1bab98300754a8e40cda58cc5481c5b5e4e52b75490c719ebd86a6e", "transactionIndex": "0x67", "logIndex": "0xad", "removed": false}], "logsBloom": "0x00000000000000000000000000000000000000000000800000000000000000000000080000000000000040040040000000000000010000000000000000000000000000000000000000902000000200000000000000000000000000000000000000000000000000000000000000000100200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004000000000000000020000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "type": "0x2", "transactionHash": "0xf4cba8f9e1bab98300754a8e40cda58cc5481c5b5e4e52b75490c719ebd86a6e", "transactionIndex": "0x67", "blockHash": "0x0000000000000000000000000000000000000000000000000000000000000000", "blockNumber": "0xb27029", "gasUsed": "0x2837a", "effectiveGasPrice": "0xf4241", "from": "0xd999ec8afacc3ddd99226adde834f1d29647d3ba", "to": "0xf7568f77fdb10c084a20c81e1d58576ab4ec850f", "contractAddress": null}], "libraries": [], "pending": [], "returns": {}, "timestamp": 1752875194, "chain": 6342, "commit": "2e969c6"}